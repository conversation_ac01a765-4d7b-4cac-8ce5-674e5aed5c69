# there is not a load balancer resource, you have to define each part separately
# https://cloud.google.com/load-balancing/docs/l7-internal/setting-up-l7-cross-reg-internal#lb-config


resource "google_compute_address" "tcp_ip" {
  count        = local.tcp_lb_enabled ? 1 : 0
  name         = "${var.app_name}-ilb-tcp-ip"
  address_type = "INTERNAL"
  subnetwork   = var.subnetwork
}

resource "google_compute_forwarding_rule" "tcp_internal" {
  count                 = local.tcp_lb_enabled ? 1 : 0
  name                  = "${var.app_name}-tcp-fwd"
  load_balancing_scheme = "INTERNAL"
  ip_protocol           = "TCP"
  backend_service       = google_compute_region_backend_service.tcp_backend.id
  ip_address            = google_compute_address.tcp_ip[0].address
  all_ports             = true
  network               = var.network_id
  subnetwork            = var.subnetwork
}

resource "google_compute_address" "udp_ip" {
  count        = local.udp_lb_enabled ? 1 : 0
  name         = "${var.app_name}-ilb-udp-ip"
  address_type = "INTERNAL"
  subnetwork   = var.subnetwork
}

resource "google_compute_forwarding_rule" "udp_internal" {
  count                 = local.udp_lb_enabled ? 1 : 0
  name                  = "${var.app_name}-udp-fwd"
  load_balancing_scheme = "INTERNAL"
  ip_protocol           = "UDP"
  backend_service       = google_compute_region_backend_service.udp_backend.id
  ip_address            = google_compute_address.udp_ip[0].address
  all_ports             = true
  network               = var.network_id
  subnetwork            = var.subnetwork
}
