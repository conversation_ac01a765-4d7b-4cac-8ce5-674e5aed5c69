resource "google_compute_region_backend_service" "tcp_backend" {
  name          = "${var.app_name}-svc-tcp"
  health_checks = [google_compute_health_check.health_check.id]
  protocol      = "TCP"
  backend {
    group          = google_compute_instance_group_manager.igm.instance_group
    balancing_mode = "CONNECTION"
  }
}

resource "google_compute_region_backend_service" "udp_backend" {
  name          = "${var.app_name}-svc-udp"
  health_checks = [google_compute_health_check.health_check.id]
  protocol      = "UDP"
  backend {
    group          = google_compute_instance_group_manager.igm.instance_group
    balancing_mode = "CONNECTION"
  }
}
