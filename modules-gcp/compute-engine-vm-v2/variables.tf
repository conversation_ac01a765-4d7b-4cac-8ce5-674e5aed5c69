variable "app_name" {
  type        = string
  description = "Name of the application"
}

variable "network_id" {
  type        = string
  description = "Network for the VM instance"
}

variable "subnetwork" {
  type        = string
  description = "Subnetwork for the VM instance"
}

variable "source_image" {
  type        = string
  description = "Source image for the VM instance"
  default     = "ubuntu-os-cloud/ubuntu-2404-lts-amd64"
}

variable "machine_type" {
  type        = string
  description = "Machine type for the VM instance"
  default     = "e2-standard-2"
}

variable "health_check_port" {
  type        = string
  description = "Port for health check"
  default     = "8080"
}

variable "metadata_startup_script" {
  type        = string
  description = "Startup script for the VM instance"
  default     = null
}

variable "load_balancer" {
  description = "Load balancer protocol type"
  type        = string
  default     = "NONE"
  validation {
    condition     = contains(["TCP", "UDP", "BOTH", "NONE"], var.load_balancer)
    error_message = "Allowed values are TCP, UDP, BOTH or NONE."
  }
}
