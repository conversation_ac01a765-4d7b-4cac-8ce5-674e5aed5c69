resource "google_compute_instance_template" "template" {
  name_prefix  = "${var.app_name}-"
  machine_type = var.machine_type

  disk {
    source_image = var.source_image
    auto_delete  = true
    boot         = true
  }

  metadata_startup_script = var.metadata_startup_script

  service_account {
    email  = google_service_account.ids.email
    scopes = ["cloud-platform"]
  }

  network_interface {
    subnetwork = var.subnetwork
  }

  lifecycle {
    create_before_destroy = true
  }
}

resource "google_compute_instance_group_manager" "igm" {
  name               = "${var.app_name}-igm"
  base_instance_name = var.app_name
  target_size        = 1

  version {
    name              = "${var.app_name}-igm-tf"
    instance_template = google_compute_instance_template.template.id
  }

  auto_healing_policies {
    health_check      = google_compute_health_check.health_check.id
    initial_delay_sec = 300
  }
}
