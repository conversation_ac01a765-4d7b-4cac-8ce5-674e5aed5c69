module "vulns_db" {
  source        = "../../../modules/dynamodb-table"
  table_name    = "${local.app_name}-vanta-vulnerabilities"
  capacity_mode = "PROVISIONED"
  read_autoscaling = {
    enabled = true
    min     = 3
    max     = 15
  }
  write_autoscaling = {
    enabled = true
    min     = 1
    max     = 3
  }

  hash_key      = "vuln-uuid"
  range_key     = "first-detected-date"
  attributes = {
    # https://developer.vanta.com/reference/listvulnerabilities
    vuln-uuid           = "S"
    first-detected-date = "S"
  }

  tags = merge(local.default_tags, {})
}

module "champions_db" {
  source            = "../../../modules/dynamodb-table"
  table_name        = "${local.app_name}-champions"
  capacity_mode     = "PROVISIONED"
  read_autoscaling  = {
    enabled = true
    min     = 1
    max     = 9
  }

  hash_key      = "email"
  attributes = {
    email = "S"
  }

  tags = merge(local.default_tags, {})
}
