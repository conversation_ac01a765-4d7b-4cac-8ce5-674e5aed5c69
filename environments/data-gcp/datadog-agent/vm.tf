module "vm" {
  source = "../../../modules-gcp/compute-engine-vm-v2"

  app_name     = "datadog-agent"
  source_image = "ubuntu-os-cloud/ubuntu-2404-lts-amd64"
  machine_type = "e2-standard-2"

  network_id = var.environment.network.name
  subnetwork = var.environment.network.subnets[0]

  health_check_port = "8126" # TCP
  load_balancer     = "BOTH"

  metadata_startup_script = templatefile("${path.module}/files/startup.sh", {
    datadog_api_key = data.google_secret_manager_secret_version_access.datadog_api_key.secret_data
  })
}
