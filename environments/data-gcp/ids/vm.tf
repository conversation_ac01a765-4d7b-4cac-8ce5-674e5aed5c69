module "vm" {
  source = "../../../modules-gcp/compute-engine-vm-v2"

  app_name     = local.app_name
  source_image = "ubuntu-os-cloud/ubuntu-2404-lts-amd64"
  machine_type = "e2-standard-2"

  network_id = var.environment.network.name
  subnetwork = var.environment.network.subnets[0]

  health_check_port = "80" # TCP
  load_balancer     = "TCP"

  metadata_startup_script = templatefile("${path.module}/files/startup.sh", {
    suricata_config    = file("${path.module}/files/suricata.yaml")
    suricata_disable   = file("${path.module}/files/disable.conf")
    suricata_logrotate = file("${path.module}/files/logrotate.conf")
    gc_ops_config      = file("${path.module}/files/ops.conf")
  })
}
