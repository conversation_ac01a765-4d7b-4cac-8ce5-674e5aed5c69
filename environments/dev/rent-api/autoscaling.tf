module "ecs_autoscaling" {
  source     = "../../../modules/ecs-auto-scaling"
  depends_on = [module.api]

  app_name            = local.app_name
  service_resource_id = module.api.resource_id

  scaling_method = "schedule"

  # shutdown at night
  scheduled_actions = [
    { cron = "0 1 * * ? *", min_capacity = 0, max_capacity = 0 },  # 22:00 ART
    { cron = "0 11 * * ? *", min_capacity = 1, max_capacity = 1 }, # 08:00 ART
  ]
}


module "ecs_autoscaling_sqs" {
  source     = "../../../modules/ecs-auto-scaling"
  depends_on = [module.write-api]

  app_name            = "${local.app_name}-write-api"
  service_resource_id = module.write-api.resource_id

  scaling_method = "queue"

  # SQS queues to monitor
  queues = [
    "dev-rent-background_tasks",
    "dev-rent-unit_rent_data",
    "dev-rent-property_concessions",
    "dev-rent-property_concessions_v2",
    "dev-rent-multifamily_units_data",
    "dev-rent-property_last_rent_data"
  ]
  threshold = 1000

  # Scaling steps configuration
  escalation_steps = [
    {
      desired_replicas = 1,
      upper_bound      = 1000
    },
    {
      desired_replicas = 2,
      lower_bound      = 1000,
      upper_bound      = 20000
    },
    {
      desired_replicas = 4,
      lower_bound      = 20000,
      upper_bound      = 30000
    },
    {
      desired_replicas = 8,
      lower_bound      = 30000,
    }
  ]

  # Set to false to scale down to 0 when queue is empty
  run_on_empty_queue = false
}
