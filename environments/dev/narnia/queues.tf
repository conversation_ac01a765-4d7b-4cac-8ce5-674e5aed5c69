module "sqs-dev-narnia-reonomy_enriched_property" {
  source     = "../../../modules/sqs-queue"
  queue_name = "dev-narnia-reonomy_enriched_property_ecs"

  tags = local.default_tags
}

module "sqs-dev-narnia-tenant_competitors_identified" {
  source     = "../../../modules/sqs-queue"
  queue_name = "dev-narnia-tenant_competitors_identified_ecs"

  tags = local.default_tags
}

module "sqs-dev-narnia-comp_set_events" {
  source     = "../../../modules/sqs-queue"
  queue_name = "dev-narnia-comp_set_events"

  tags = local.default_tags
}

module "sns-dev-narnia-medical_off_market_property_news" {
  source         = "../../../modules/sns-topic"
  sns_topic_name = "dev-narnia-medical_off_market_property_news"
  sqs_subscriptions = [
    "arn:aws:sqs:us-east-1:681574592108:dev-property_assets-medical_off_market_property_news",
  ]

  tags = local.default_tags
}

module "sns-dev-narnia-medical_on_market_property_news" {
  source         = "../../../modules/sns-topic"
  sns_topic_name = "dev-narnia-medical_on_market_property_news"
  sqs_subscriptions = [
    "arn:aws:sqs:us-east-1:681574592108:dev-property_assets-medical_on_market_property_news",
  ]

  tags = local.default_tags
}

module "sns-dev-narnia-medical_office_calculated_values" {
  source         = "../../../modules/sns-topic"
  sns_topic_name = "dev-narnia-medical_office_calculated_values"
  sqs_subscriptions = [
    "arn:aws:sqs:us-east-1:681574592108:dev-property_assets-medical_office_calculated_values",
  ]

  tags = local.default_tags
}

module "sns-dev-narnia-multifamily_strategic_scores" {
  source         = "../../../modules/sns-topic"
  sns_topic_name = "dev-narnia-multifamily_strategic_scores"
  sqs_subscriptions = [
    "arn:aws:sqs:us-east-1:681574592108:dev-property_assets-multifamily_strategic_scores",
  ]

  tags = local.default_tags
}

module "sns-dev-narnia-multifamily_summarized_property_news" {
  source         = "../../../modules/sns-topic"
  sns_topic_name = "dev-narnia-multifamily_summarized_property_news"
  sqs_subscriptions = [
    "arn:aws:sqs:us-east-1:681574592108:dev-property_assets-multifamily_summarized_property_news",
  ]

  tags = local.default_tags
}

module "sns-dev-narnia-multifamily_estimated_prices" {
  source         = "../../../modules/sns-topic"
  sns_topic_name = "dev-narnia-multifamily_estimated_prices"
  sqs_subscriptions = [
    "arn:aws:sqs:us-east-1:681574592108:dev-property_assets-multifamily_estimated_prices",
  ]

  tags = local.default_tags
}

module "sns-dev-narnia-zipcodes_strategic_scores" {
  source         = "../../../modules/sns-topic"
  sns_topic_name = "dev-narnia-zipcodes_strategic_scores"
  sqs_subscriptions = [
    "arn:aws:sqs:us-east-1:681574592108:dev-demographics-zipcodes_strategic_scores",
  ]

  tags = local.default_tags
}

module "sns-dev-narnia-school_metrics" {
  source         = "../../../modules/sns-topic"
  sns_topic_name = "dev-narnia-school_metrics"
  sqs_subscriptions = [
    "arn:aws:sqs:us-east-1:681574592108:dev-property_assets-school_metrics",
  ]

  tags = local.default_tags
}

module "sns-dev-narnia-flood_zone_metrics" {
  source         = "../../../modules/sns-topic"
  sns_topic_name = "dev-flood_zone_metrics"
  sqs_subscriptions = [
    "arn:aws:sqs:us-east-1:681574592108:dev-property_assets-flood_zone_metrics",
  ]

  tags = local.default_tags
}

module "sns-dev-narnia-debt_and_transactability" {
  source         = "../../../modules/sns-topic"
  sns_topic_name = "dev-narnia-debt_and_transactability"
  sqs_subscriptions = [
    "arn:aws:sqs:us-east-1:681574592108:dev-property_assets-debt_and_transactability",
  ]

  tags = local.default_tags
}

module "sns-dev-narnia-multifamily_rent_percentiles" {
  source         = "../../../modules/sns-topic"
  sns_topic_name = "dev-narnia-multifamily-rent-percentiles"
  sqs_subscriptions = [
    "arn:aws:sqs:us-east-1:681574592108:dev-property_assets-multifamily_rent_percentiles",
  ]

  tags = local.default_tags
}

module "sns-dev-narnia-multifamily_rent_thermometers" {
  source         = "../../../modules/sns-topic"
  sns_topic_name = "dev-narnia-multifamily_rent_thermometers"
  sqs_subscriptions = [
    "arn:aws:sqs:us-east-1:681574592108:dev-property_assets-multifamily_rent_thermometers",
  ]

  tags = local.default_tags
}

module "sns-dev-narnia-multifamily_rent_thermometer_scores" {
  source         = "../../../modules/sns-topic"
  sns_topic_name = "dev-narnia-multifamily_rent_thermometer_scores"
  sqs_subscriptions = [
    "arn:aws:sqs:us-east-1:681574592108:dev-property_assets-multifamily_rent_thermometer_scores",
  ]

  tags = local.default_tags
}

module "sns-dev-narnia-asset_manager_yardi_news" {
  source            = "../../../modules/sns-topic"
  sns_topic_name    = "dev-narnia-asset_manager_news"
  sqs_subscriptions = ["arn:aws:sqs:us-east-1:681574592108:dev-asset_manager-yardi-notifications"]

  tags = local.default_tags
}

module "sns-dev-narnia-zip_mf_units_summarized" {
  source            = "../../../modules/sns-topic"
  sns_topic_name    = "dev-narnia-zip_mf_units_summarized"
  sqs_subscriptions = ["arn:aws:sqs:us-east-1:681574592108:dev-demographics-zip_mf_units_summarized"]

  tags = local.default_tags
}

module "sns-dev-narnia-zip_news" {
  source            = "../../../modules/sns-topic"
  sns_topic_name    = "dev-narnia-zip_news"
  sqs_subscriptions = ["arn:aws:sqs:us-east-1:681574592108:dev-demographics-zip_news"]

  tags = local.default_tags
}

module "sns-dev-narnia-unit_rent_data" {
  source                = "../../../modules/sns-topic"
  sns_topic_name        = "dev-narnia-rent_data"
  raw_sqs_subscriptions = ["arn:aws:sqs:us-east-1:681574592108:dev-rent-unit_rent_data"]
  tags                  = local.default_tags
}

module "sns-dev-narnia-property_concessions_fifo" {
  source                = "../../../modules/sns-topic"
  sns_topic_name        = "dev-narnia-property_concessions"
  fifo_topic            = true
  tags                  = local.default_tags
}

module "sns-dev-narnia-property_concessions" {
  source                = "../../../modules/sns-topic"
  sns_topic_name        = "dev-narnia-property_concessions"
  raw_sqs_subscriptions = ["arn:aws:sqs:us-east-1:681574592108:dev-rent-property_concessions"]
  fifo_topic            = false
  tags                  = local.default_tags
}

module "sns-dev-narnia-property_concessions_v2" {
  source                = "../../../modules/sns-topic"
  sns_topic_name        = "dev-narnia-property_concessions_v2"
  raw_sqs_subscriptions = ["arn:aws:sqs:us-east-1:681574592108:dev-rent-property_concessions_v2"]
  fifo_topic            = false
  tags                  = local.default_tags
}


module "sns-dev-narnia-multifamily_estimated_values" {
  source         = "../../../modules/sns-topic"
  sns_topic_name = "dev-narnia-multifamily_estimated_values"
  sqs_subscriptions = [
    "arn:aws:sqs:us-east-1:681574592108:dev-property_assets-multifamily_estimated_values",
  ]

  tags = local.default_tags
}

module "sns-dev-narnia-property_multifamily_contacts" {
  source         = "../../../modules/sns-topic"
  sns_topic_name = "dev-narnia-property_multifamily_contacts"
  sqs_subscriptions = [
    "arn:aws:sqs:us-east-1:681574592108:dev-property_assets-property_multifamily_contacts",
  ]

  tags = local.default_tags
}

module "sns-dev-narnia-properties_units_data" {
  source         = "../../../modules/sns-topic"
  sns_topic_name = "dev-narnia-properties_units_data"
  raw_sqs_subscriptions = [
    "arn:aws:sqs:us-east-1:681574592108:dev-property_assets-properties_units_data"
  ]
  tags = local.default_tags
}

module "sns-dev-narnia-properties_quality_data" {
  source         = "../../../modules/sns-topic"
  sns_topic_name = "dev-narnia-properties_quality_data"
  raw_sqs_subscriptions = [
    "arn:aws:sqs:us-east-1:681574592108:dev-property_assets-properties_quality_data"
  ]
  tags = local.default_tags
}
