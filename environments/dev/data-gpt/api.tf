module "api" {
  app_name = local.ecs_name
  cluster  = var.environment.cluster
  source   = "../../../modules/ecs-service-deployment-v2"

  container_size = {
    cpu    = 2048
    memory = 4096
  }

  private_network = var.environment.private_network
  public_endpoint = true

  port = 8080

  idle_timeout = 600

  iam_extra_policies = ["arn:aws:iam::aws:policy/AmazonBedrockFullAccess"]

  chatbot_arn = var.environment.chatbot_arn

  github_repository  = "unlockre/data-gpt-api"
  github_branch_name = "develop"
  redeploy_schedule  = "cron(0 12 ? * MON *)" # Every Monday at 12:00 UTC

  codebuild_migration_stage = false

  ecs_variables = {
    # VAR_NAME = "string"
    SESSION_COLLECTION = "gpt-sessions-dev"
    FILES_COLLECTION   = "gpt-files-dev"
    METADATA_BUCKET    = "property-analyzer-files-dev"

    ENV   = "dev"
    SCOPE = "server"
    PORT  = "8080"

    OPENAI_API_VERSION_AZURE = "2025-03-01-preview"
    OPENAI_API_BASE_AZURE    = "https://keyway-data-azure-openai.openai.azure.com"
    # OPENAI_API_BASE_AZURE    = "https://keyway-data-azure-openai-west.openai.azure.com"

    USE_AZURE = "1"
    DAYS_TTL  = "2"

    LANGCHAIN_TRACING_V2 = "true"
    LANGCHAIN_ENDPOINT   = "https://api.smith.langchain.com"
    LANGCHAIN_PROJECT    = "data-gpt-api-dev"

    PROPERTY_SEARCH_ENDPOINT    = "https://property-search-api.dev.whykeyway.com"
    PROPERTY_SAGE_BASE_URL      = "https://dev-property-sage.vercel.app"
    PROPERTY_SAGE_ENDPOINT      = "https://dev-property-sage.vercel.app"
    KEYBRAIN_BASE_URL           = "https://dev-keybrain.vercel.app"
    KEYBRAIN_ENDPOINT           = "https://dev-keybrain.vercel.app"
    COMPS_API_ENDPOINT          = "https://comps-api.dev.whykeyway.com"
    COMPS_ENDPOINT              = "https://comps-api.dev.whykeyway.com"
    COMP_SELECTOR_ENDPOINT      = "https://comp-selector-api.dev.whykeyway.com"
    KFILE_HOST                  = "https://kfile-api.dev.whykeyway.com"
    DEMOGRAPHICS_ENDPOINT       = "https://demographics-api.dev.whykeyway.com"
    PROPERTY_ASSETS_ENDPOINT    = "https://property-assets-api.dev.whykeyway.com"
    DEALROOM_API                = "https://deal-room-api.dev.whykeyway.com"
    DEALROOM_ENDPOINT           = "https://deal-room-api.dev.whykeyway.com"
    DEALROOM_HOME_ENDPOINT      = "https://deal-room-admin-dev.vercel.app"
    PROPERTY_ANALYZER_ENDPOINT  = "https://property-analyzer-api.dev.whykeyway.com"
    NLM_INGESTOR_ENDPOINT       = "https://nlm-ingestor.whykeyway.com"
    OPENAI_API_ENDPOINT         = "https://api.openai.com"
    DOCUMENT_PARSER_ENDPOINT    = "https://document-parser.dev.whykeyway.com"
    KEY_ASSIST_ENDPOINT         = "https://key-assist-api.dev.whykeyway.com"
    SPLIT_PDF_DOCUMENT_ENDPOINT = "https://keydocs-pdf-image.dev.whykeyway.com"
    RENT_API_ENDPOINT           = "https://rent-api.dev.whykeyway.com"
    KEY_READER_ENDPOINT         = "https://keyreader-api-395007346027.us-central1.run.app"

    DEAL_MEMBERS = "{\"dealMembers\": [543, 495], \"leaderId\": 495}"

    CLASSIFY_FILE_TOPIC                   = "dev-data_gpt-classify_file"
    EXTRACT_FILE_DATA_TOPIC               = "dev-data_gpt-extract_file_data"
    EXTRACT_PROPERTY_DATA_FROM_FILE_TOPIC = "dev-data_gpt-property_data_news"
    EXTRACT_METADATA_FROM_FILE_TOPIC      = "dev-data_gpt-metadata_news"
    EXTRACTORS_FROM_FILE_TOPIC            = "dev-data_gpt-extractors_news"

    DD_PROFILING_ENABLED          = "true"
    DATADOG_ENABLED               = "true"
    DD_RUNTIME_METRICS_ENABLED    = "true"
    DD_PROFILING_TIMELINE_ENABLED = "true"
  }

  ecs_secrets = {
    # VAR_NAME = "/env/app/parameter_name"
    ACCOUNT_ID            = "/any/aws/account-id"
    REGION_NAME           = "/any/aws/region"
    AWS_ACCESS_KEY_ID     = "/any/aws/access-key-id"
    AWS_ACCESS_KEY        = "/any/aws/access-key-id"
    AWS_SECRET_ACCESS_KEY = "/any/aws/secret-access-key"
    GH_SSH_PRIVATE_KEY    = "/any/github/private-ssh-key" # note: used for codebuild, remove later

    GCP_KEYS_B64         = "${local.ecs_parameter_prefix}/GCP_KEYS_B64"
    OPENAI_API_KEY       = "${local.ecs_parameter_prefix}/OPENAI_API_KEY"
    AUTH_TOKEN           = "${local.ecs_parameter_prefix}/AUTH_TOKEN"
    SLACK_TOKEN          = "${local.ecs_parameter_prefix}/SLACK_TOKEN"
    OPENAI_API_KEY_AZURE = "${local.ecs_parameter_prefix}/OPENAI_API_KEY_AZURE" # OPENAI_API_KEY_AZURE_WEST
    LANGCHAIN_API_KEY    = "${local.ecs_parameter_prefix}/LANGCHAIN_API_KEY"
    GOOGLE_API_KEY       = "${local.ecs_parameter_prefix}/GOOGLE_API_KEY"
    ANTHROPIC_API_KEY    = "${local.ecs_parameter_prefix}/ANTHROPIC_API_KEY"

    ZENROWS_USER                 = "${local.ecs_parameter_prefix}/ZENROWS_USER"
    ZENROWS_PWD                  = "${local.ecs_parameter_prefix}/ZENROWS_PWD"
    GOOGLE_SEARCH_API_KEY        = "${local.ecs_parameter_prefix}/GOOGLE_SEARCH_API_KEY"
    GOOGLE_SEARCH_CSE_ID         = "${local.ecs_parameter_prefix}/GOOGLE_SEARCH_CSE_ID"
    PROPERTY_ANALYZER_SECRET_KEY = "/any/property-analyzer-api/keyway-key"

    # Number of pages to retrieve from the documents
    SEARCH_K = "${local.ecs_parameter_prefix}/SEARCH_K"
  }

  tags = local.default_tags
}
