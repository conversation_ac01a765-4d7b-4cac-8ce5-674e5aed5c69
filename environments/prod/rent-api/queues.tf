module "sqs-prod-rent-prop_unit_rent_data" {
  source            = "../../../modules/sqs-queue"
  queue_name        = "prod-rent-unit_rent_data"
  tags              = local.default_tags
  max_receive_count = 3
}

module "sqs-prod-rent-property_concessions" {
  source            = "../../../modules/sqs-queue"
  queue_name        = "prod-rent-property_concessions"
  fifo_queue        = false
  tags              = local.default_tags
  max_receive_count = 3
}

module "sqs-prod-rent-property_concessions_v2" {
  source            = "../../../modules/sqs-queue"
  queue_name        = "prod-rent-property_concessions_v2"
  fifo_queue        = false
  tags              = local.default_tags
  max_receive_count = 3
}

module "sqs-prod-rent-background_tasks" {
  source            = "../../../modules/sqs-queue"
  queue_name        = "prod-rent-background_tasks"
  tags              = local.default_tags
  max_receive_count = 3
}

module "sqs-prod-rent-multifamily_units_data" {
  source            = "../../../modules/sqs-queue"
  queue_name        = "prod-rent-multifamily_units_data"
  tags              = local.default_tags
  max_receive_count = 3
}

module "sqs-prod-rent-property_last_rent_data" {
  source            = "../../../modules/sqs-queue"
  queue_name        = "prod-rent-property_last_rent_data"
  tags              = local.default_tags
  max_receive_count = 3
}